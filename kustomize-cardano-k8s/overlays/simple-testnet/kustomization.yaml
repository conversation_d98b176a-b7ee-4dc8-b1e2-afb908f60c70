apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# Metadata for this overlay
metadata:
  name: cardano-testnet-simple
  annotations:
    config.kubernetes.io/local-config: "true"

# Common labels for all resources in this overlay
commonLabels:
  environment: testnet
  cardano.network: testnet
  deployment.type: simple

# Common annotations
commonAnnotations:
  description: "Simplified Cardano testnet deployment for educational purposes"
  maintainer: "cardano-k8s-simple"

# Base resources to include
resources:
- namespace.yaml
- ../../base/common-simple
- ../../base/postgres-simple
- ../../base/cardano-node-simple
- ../../base/cardano-db-sync-simple

# Patches to customize for testnet
patchesStrategicMerge:
- network-config-patch.yaml
- storage-patch.yaml

# ConfigMap generator to override network settings
configMapGenerator:
- name: common-env
  behavior: merge
  literals:
  - NETWORK=testnet

# Namespace for this deployment
namespace: cardano-testnet
