# Storage patches for mainnet - larger storage requirements
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: cardano-node
spec:
  volumeClaimTemplates:
  - metadata:
      name: node-db
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 120Gi  # Mainnet requires significant storage
  - metadata:
      name: node-ipc
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 1Gi  # IPC socket storage

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: cardano-db-sync
spec:
  volumeClaimTemplates:
  - metadata:
      name: db-sync-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 20Gi  # DB Sync state data
  - metadata:
      name: node-ipc
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 1Gi  # IPC socket storage

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
spec:
  volumeClaimTemplates:
  - metadata:
      name: postgres-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 200Gi  # Mainnet database is very large
