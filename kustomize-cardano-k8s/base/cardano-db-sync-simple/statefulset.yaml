apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: cardano-db-sync
  labels:
    app.kubernetes.io/component: cardano-db-sync
    app.kubernetes.io/part-of: cardano-stack
spec:
  serviceName: cardano-db-sync
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/component: cardano-db-sync
      app.kubernetes.io/part-of: cardano-stack
  template:
    metadata:
      labels:
        app.kubernetes.io/component: cardano-db-sync
        app.kubernetes.io/part-of: cardano-stack
    spec:
      initContainers:
      # Clean up any lost+found directories
      - name: cleanup-volumes
        image: busybox:1.36
        command: ["sh", "-c", "rm -rf /var/lib/cexplorer/lost+found /node-ipc/lost+found || true"]
        volumeMounts:
        - name: db-sync-data
          mountPath: /var/lib/cexplorer
        - name: node-ipc
          mountPath: /node-ipc
      containers:
      - name: cardano-db-sync
        image: ghcr.io/intersectmbo/cardano-db-sync:********
        command: ["/bin/bash", "/scripts/entrypoint.sh"]
        env:
        - name: NETWORK
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: NETWORK
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_HOST
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_PORT
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: POSTGRES_PASSWORD
        - name: CARDANO_NODE_SOCKET_PATH
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_PATH
        ports:
        - containerPort: 8080
          name: prometheus
          protocol: TCP
        volumeMounts:
        - name: db-sync-data
          mountPath: /var/lib/cexplorer
        - name: node-ipc
          mountPath: /node-ipc
        - name: cardano-db-sync-config
          mountPath: /config
        - name: cardano-db-sync-config
          mountPath: /scripts
        resources:
          requests:
            memory: "4Gi"
            cpu: "1"
          limits:
            memory: "8Gi"
            cpu: "4"
        # Health check using our custom script
        livenessProbe:
          exec:
            command: ["/bin/bash", "/scripts/healthcheck.sh"]
          initialDelaySeconds: 600
          periodSeconds: 60
          timeoutSeconds: 30
          failureThreshold: 3
        readinessProbe:
          exec:
            command: ["/bin/bash", "/scripts/healthcheck.sh"]
          initialDelaySeconds: 300
          periodSeconds: 30
          timeoutSeconds: 30
          failureThreshold: 5
      volumes:
      - name: cardano-db-sync-config
        configMap:
          name: cardano-db-sync-config
          defaultMode: 0755
  volumeClaimTemplates:
  - metadata:
      name: db-sync-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 4Gi
  - metadata:
      name: node-ipc
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 1Gi
