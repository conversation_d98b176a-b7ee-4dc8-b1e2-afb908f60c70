apiVersion: v1
kind: ConfigMap
metadata:
  name: common-env
  labels:
    app.kubernetes.io/component: common
    app.kubernetes.io/part-of: cardano-stack
data:
  # PostgreSQL connection settings
  POSTGRES_HOST: "postgres"
  POSTGRES_PORT: "5432"
  POSTGRES_DB: "cexplorer"
  POSTGRES_USER: "postgres"
  
  # Cardano Node settings
  CARDANO_NODE_SOCKET_PATH: "/ipc/node.socket"
  
  # Network configuration (will be overridden in overlays)
  NETWORK: "mainnet"
