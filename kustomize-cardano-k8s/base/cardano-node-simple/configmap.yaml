apiVersion: v1
kind: ConfigMap
metadata:
  name: cardano-node-config
  labels:
    app.kubernetes.io/component: cardano-node
    app.kubernetes.io/part-of: cardano-stack
data:
  # Simplified entrypoint script for educational purposes
  entrypoint.sh: |
    #!/bin/bash
    set -e
    
    echo "Starting Cardano Node for network: ${NETWORK}"
    echo "Socket path: ${CARDANO_NODE_SOCKET_PATH}"
    echo "Data directory: /data/db"
    
    # Create socket directory if it doesn't exist
    mkdir -p "$(dirname "${CARDANO_NODE_SOCKET_PATH}")"
    
    # Start cardano-node with simplified configuration
    exec cardano-node run \
      --topology /config/topology.json \
      --database-path /data/db \
      --socket-path "${CARDANO_NODE_SOCKET_PATH}" \
      --config /config/config.json
  
  # Basic topology configuration - will be overridden in network-specific overlays
  topology.json: |
    {
      "localRoots": [
        {
          "accessPoints": [],
          "advertise": false,
          "valency": 1
        }
      ],
      "publicRoots": [
        {
          "accessPoints": [
            {
              "address": "relays-new.cardano-mainnet.iohk.io",
              "port": 3001
            }
          ],
          "advertise": false
        }
      ],
      "useLedgerAfterSlot": -1
    }
  
  # Basic node configuration - will be overridden in network-specific overlays  
  config.json: |
    {
      "ApplicationName": "cardano-sl",
      "ApplicationVersion": 1,
      "Protocol": "Cardano",
      "RequiresNetworkMagic": "RequiresNoMagic",
      "EnableLogMetrics": true,
      "EnableLogging": true,
      "TurnOnLogMetrics": true,
      "TurnOnLogging": true,
      "defaultBackends": [
        "KatipBK"
      ],
      "defaultScribes": [
        [
          "StdoutSK",
          "stdout"
        ]
      ],
      "minSeverity": "Info",
      "options": {
        "mapBackends": {
          "cardano.node.metrics": [
            "EKGViewBK"
          ]
        }
      },
      "setupBackends": [
        "KatipBK",
        "EKGViewBK"
      ],
      "setupScribes": [
        {
          "scKind": "StdoutSK",
          "scName": "stdout",
          "scRotation": null
        }
      ]
    }
